import React, { useState, useEffect } from "react";
import { usePortfolioContent } from "@/hooks/usePortfolioContent";
import { useHeroContent } from "@/hooks/useHeroContent";
import { supabase } from "@/integrations/supabase/client";

const About = () => {
  // Database integration
  const { content } = usePortfolioContent();
  const { heroContent } = useHeroContent();

  // About content state with defaults
  const [aboutContent, setAboutContent] = useState({
    title: "About Me",
    description: "I'm a creative full stack developer specializing in building exceptional digital experiences. I build blazing-fast, scalable web apps—from pixel-perfect UIs to robust backends. Currently crafting AI-powered solutions and open-source tools to simplify workflows.",
    experience: "2+ Years",
    projects: "20+ Projects",
    clients: "15+ Clients",
    highlightedWords: [], // Words to highlight in description
    features: [
      {
         id: 1,
        title: "Problem-Solving Approach",
        description: "I don’t just write code—I solve business challenges with technology.",
        icon: "🎯",
      },
      {
         id: 2,
        title: "Website Development",
        description: "I build fast, secure, and scalable modern web apps with modern design principles",
        icon: "💻",
      },
      {
         id: 3,
        title: "Marketing & Reporting",
        description: "I optimize apps for real-world impact and Data-Driven Decisions.",
        icon: "📊",
      },
       {
        id: 4,
        title: "Client Collaboration",
        description: "I bridge tech and business needs with a comprehensive negotiation.",
        icon: "🤝",
      },
       {
        id: 5,
        title: "From Idea to MVP in Days",
        description: "I accelerate development without sacrificing quality",
        icon: "🚀",
      },
    ],
  });

  // Function to render description with highlighted words and multiple paragraphs
  const renderDescriptionWithHighlights = (text, highlightedWords = []) => {
    // Split text by double line breaks to create paragraphs
    const paragraphs = text.split('\n\n').filter(p => p.trim());

    return paragraphs.map((paragraph, index) => {
      let highlightedText = paragraph.trim();

      if (highlightedWords && highlightedWords.length > 0) {
        highlightedWords.forEach((word) => {
          const regex = new RegExp(`\\b(${word})\\b`, 'gi');
          highlightedText = highlightedText.replace(
            regex,
            '<span class="highlight-keyword">$1</span>'
          );
        });
      }

      return (
        <p
          key={index}
          className={`about-description text-lg leading-relaxed ${index > 0 ? 'mt-4' : ''}`}
          dangerouslySetInnerHTML={{ __html: highlightedText }}
        />
      );
    });
  };

  // Load content from database
  useEffect(() => {
    if (content && content.length > 0) {
      const aboutData = content.find(item => item.section === "about")?.content;
      if (aboutData) {

        setAboutContent(prev => ({
          ...prev,
          ...aboutData,
        }));
      }
    }
  }, [content]);

  // Real-time subscription for about content updates
  useEffect(() => {
    const channel = supabase
      .channel("about_content_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "portfolio_content",
          filter: "section=eq.about",
        },
        (payload) => {
          if (
            payload.eventType === "UPDATE" ||
            payload.eventType === "INSERT"
          ) {
            const newContent = payload.new as any;
            if (newContent.content) {
              setAboutContent(prev => ({
                ...prev,
                ...newContent.content,
              }));
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return (
    <section
      id="about"
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="about-bg-glow"></div>
      <div className="about-particles"></div>

      <div className="container mx-auto relative z-10">
        <div className="flex flex-col md:flex-row gap-12 lg:gap-16">
          {/* About Content */}
          <div className="w-full md:w-1/2">
            {/* Enhanced Title */}
            <div className="relative mb-8">
              <h2 className="enhanced-section-title">About Me</h2>
              <div className="title-glow-effect"></div>
              <div className="title-underline-animated"></div>
            </div>

            {/* Enhanced Description */}
            <div className="space-y-6">
              <div className="about-text-block">
                {renderDescriptionWithHighlights(aboutContent.description, aboutContent.highlightedWords)}
              </div>
            </div>

            {/* Enhanced Personal Info Grid */}
            <div className="grid grid-cols-2 gap-6 mt-10">
              <div className="info-item">
                <h6 className="info-label">Name:</h6>
                <p className="info-value">{heroContent.name}</p>
                <div className="info-glow"></div>
              </div>
              <div className="info-item">
                <h6 className="info-label">Profession:</h6>
                <p className="info-value">{heroContent.profession}</p>
                <div className="info-glow"></div>
              </div>
              <div className="info-item">
                <h6 className="info-label">Experience:</h6>
                <p className="info-value">{aboutContent.experience}</p>
                <div className="info-glow"></div>
              </div>
              <div className="info-item">
                <h6 className="info-label">Projects:</h6>
                <p className="info-value">{aboutContent.projects}</p>
                <div className="info-glow"></div>
              </div>
            </div>

            {/* Stats Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-10">
              <div className="stat-card">
                <div className="stat-number">{aboutContent.experience}</div>
                <div className="stat-label">Experience</div>
                <div className="stat-glow"></div>
              </div>
              <div className="stat-card">
                <div className="stat-number">{aboutContent.projects}</div>
                <div className="stat-label">Completed Projects</div>
                <div className="stat-glow"></div>
              </div>
              <div className="stat-card">
                <div className="stat-number">{aboutContent.clients}</div>
                <div className="stat-label">Happy Clients</div>
                <div className="stat-glow"></div>
              </div>
            </div>
          </div>

          {/* Enhanced Features */}
          <div className="w-full md:w-1/2">
            <div className="grid grid-cols-1 gap-8">
              {aboutContent.features.map((feature, index) => (
                <div
                  key={feature.id}
                  className="enhanced-feature-card group"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="feature-icon-container">
                    <div className="feature-icon">{feature.icon}</div>
                    <div className="icon-glow"></div>
                    <div className="icon-particles"></div>
                  </div>

                  <div className="feature-content">
                    <h3 className="feature-title">{feature.title}</h3>
                    <p className="feature-description">{feature.description}</p>
                  </div>

                  <div className="card-glow-effect"></div>
                  <div className="card-border-animation"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
