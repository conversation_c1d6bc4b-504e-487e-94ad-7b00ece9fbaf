<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OG Image Generator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .og-image {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            position: relative;
            margin: 0 auto;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .logo-container {
            position: absolute;
            left: 50%;
            top: 115px;
            transform: translateX(-50%);
        }
        
        .k-letter {
            width: 384px;
            height: 468px;
            position: relative;
        }
        
        .k-vertical {
            position: absolute;
            left: 0;
            top: 0;
            width: 81px;
            height: 468px;
            background: linear-gradient(135deg, #ff014f 0%, #f9004d 100%);
            border-radius: 40px;
        }
        
        .k-upper {
            position: absolute;
            left: 105px;
            top: 0;
            width: 279px;
            height: 129px;
            background: linear-gradient(135deg, #ff014f 0%, #f9004d 100%);
            transform: skew(-20deg);
            transform-origin: left bottom;
        }
        
        .k-lower {
            position: absolute;
            left: 105px;
            bottom: 0;
            width: 279px;
            height: 129px;
            background: linear-gradient(135deg, #ff014f 0%, #f9004d 100%);
            transform: skew(20deg);
            transform-origin: left top;
        }
        
        .text-container {
            position: absolute;
            bottom: 150px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: white;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: bold;
            margin: 0 0 20px 0;
            color: white;
        }
        
        .subtitle {
            font-size: 32px;
            margin: 0 0 15px 0;
            color: #94a3b8;
            font-weight: normal;
        }
        
        .details {
            font-size: 24px;
            margin: 0;
            color: #64748b;
            font-weight: normal;
        }
        
        .glow-border {
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 4px solid;
            border-image: linear-gradient(135deg, #ff014f, #f9004d) 1;
            border-radius: 16px;
            opacity: 0.3;
        }
        
        .instructions {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            max-width: 1200px;
            margin: 20px auto;
        }
        
        .download-btn {
            background: #ff014f;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        
        .download-btn:hover {
            background: #f9004d;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h1>OG Image Generator for Kenenisa Kero</h1>
        <p>This page generates the Open Graph image for your portfolio. Use the buttons below to capture or download the image.</p>
        <button class="download-btn" onclick="captureImage()">📸 Capture as PNG</button>
        <button class="download-btn" onclick="downloadHTML()">💾 Download HTML</button>
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Click "Capture as PNG" to save the image</li>
            <li>Or use browser developer tools to take a screenshot</li>
            <li>Save as "og-image.png" in your public folder</li>
            <li>Update your meta tags to use the PNG version</li>
        </ol>
    </div>

    <div class="og-image" id="ogImage">
        <div class="glow-border"></div>
        
        <div class="logo-container">
            <div class="k-letter">
                <div class="k-vertical"></div>
                <div class="k-upper"></div>
                <div class="k-lower"></div>
            </div>
        </div>
        
        <div class="text-container">
            <div class="main-title">Kenenisa Kero (Kenenis)</div>
            <div class="subtitle">Full Stack Developer & UI/UX Designer</div>
            <div class="details">React • Node.js • TypeScript • Bursa, Turkey</div>
        </div>
    </div>

    <script>
        async function captureImage() {
            try {
                // Try to use html2canvas if available
                if (typeof html2canvas !== 'undefined') {
                    const canvas = await html2canvas(document.getElementById('ogImage'), {
                        width: 1200,
                        height: 630,
                        scale: 1
                    });
                    
                    const link = document.createElement('a');
                    link.download = 'og-image.png';
                    link.href = canvas.toDataURL();
                    link.click();
                } else {
                    alert('Please use browser developer tools to take a screenshot of the image area, or install html2canvas library.');
                }
            } catch (error) {
                alert('Screenshot failed. Please use browser developer tools to manually capture the image.');
            }
        }
        
        function downloadHTML() {
            const htmlContent = document.documentElement.outerHTML;
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const link = document.createElement('a');
            link.download = 'og-image-generator.html';
            link.href = URL.createObjectURL(blob);
            link.click();
        }
        
        // Add html2canvas library
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
        document.head.appendChild(script);
    </script>
</body>
</html>
