// SEO Configuration for <PERSON><PERSON><PERSON> (Kenenis) Portfolio

export const seoConfig = {
  // Basic Information
  name: "<PERSON><PERSON><PERSON>",
  nickname: "<PERSON><PERSON><PERSON>",
  domain: "kenenis.com",
  url: "https://kenenis.com",
  email: "<EMAIL>",
  
  // Professional Information
  title: "<PERSON><PERSON><PERSON> (Kenenis) - Full Stack Developer & UI/UX Designer | Portfolio",
  description: "Expert Full Stack Developer specializing in React, Node.js, TypeScript & UI/UX Design. 2+ years experience, 20+ projects completed. Available for freelance work.",
  
  // Keywords for SEO
  keywords: [
    // Name variations
    "Kenenisa Kero", "Kenenis", "Kenenis Dev",
    
    // Professional titles
    "Full Stack Developer", "React Developer", "Node.js Developer", 
    "TypeScript Developer", "UI/UX Designer", "Web Developer", 
    "Software Engineer", "Frontend Developer", "Backend Developer",
    
    // Technologies
    "React.js", "Node.js", "TypeScript", "JavaScript", "HTML", "CSS",
    "Express.js", "MySQL", "REST API", "Bootstrap", "Tailwind CSS",
    "Git", "GitHub", "WordPress", "Adobe Photoshop", "Figma",
    
    // Services
    "Web Development", "Full Stack Development", "Frontend Development",
    "Backend Development", "UI/UX Design", "Website Design",
    "Web Application Development", "Custom Web Solutions",
    
    // Location & Availability
    "Ethiopia Developer", "Ethiopian Web Developer", "Freelancer",
    "Remote Developer", "Freelance Web Developer",
    
    // Portfolio related
    "Portfolio", "Projects", "Web Portfolio", "Developer Portfolio"
  ],
  
  // Social Media
  social: {
    twitter: "@kenenis",
    github: "https://github.com/kenenis",
    linkedin: "https://linkedin.com/in/kenenis",
    email: "<EMAIL>"
  },
  
  // Images
  images: {
    logo: "/favicon.svg",
    ogImage: "/og-image.svg",
    avatar: "/apple-touch-icon.svg"
  },
  
  // Professional Stats
  stats: {
    experience: "2+ Years",
    projects: "20+ Projects", 
    clients: "15+ Clients",
    technologies: "10+ Technologies"
  },
  
  // Services offered
  services: [
    "Full Stack Web Development",
    "Frontend Development (React.js, TypeScript)",
    "Backend Development (Node.js, Express.js)",
    "UI/UX Design",
    "Database Design (MySQL)",
    "API Development (REST)",
    "Responsive Web Design",
    "WordPress Development",
    "E-commerce Solutions",
    "Web Application Development"
  ],
  
  // Technologies expertise
  technologies: {
    frontend: ["React.js", "TypeScript", "JavaScript", "HTML5", "CSS3", "Tailwind CSS", "Bootstrap"],
    backend: ["Node.js", "Express.js", "MySQL", "REST API"],
    tools: ["Git", "GitHub", "Figma", "Adobe Photoshop", "WordPress", "Bash Script"],
    design: ["UI/UX Design", "Responsive Design", "Mobile-First Design"]
  },
  
  // Page-specific SEO
  pages: {
    home: {
      title: "Kenenisa Kero (Kenenis) - Full Stack Developer & UI/UX Designer | Portfolio",
      description: "Expert Full Stack Developer specializing in React, Node.js, TypeScript & UI/UX Design. 2+ years experience, 20+ projects completed. Available for freelance work.",
      keywords: "Kenenisa Kero, Kenenis, Full Stack Developer, React Developer, Portfolio"
    },
    about: {
      title: "About Kenenisa Kero (Kenenis) - Full Stack Developer | Experience & Skills",
      description: "Learn about Kenenisa Kero (Kenenis), a passionate Full Stack Developer with 2+ years experience in React, Node.js, TypeScript, and UI/UX Design. Based in Ethiopia, available worldwide.",
      keywords: "About Kenenis, Kenenisa Kero background, Full Stack Developer experience, React developer Ethiopia"
    },
    portfolio: {
      title: "Portfolio - Kenenisa Kero (Kenenis) | 20+ Web Development Projects",
      description: "Explore 20+ web development projects by Kenenisa Kero (Kenenis). Full stack applications, React.js websites, UI/UX designs, and innovative digital solutions.",
      keywords: "Kenenis portfolio, Kenenisa Kero projects, React projects, Full stack projects, Web development portfolio"
    },
    skills: {
      title: "Skills & Technologies - Kenenisa Kero (Kenenis) | React, Node.js, TypeScript",
      description: "Technical skills and expertise of Kenenisa Kero (Kenenis): React.js (90%), Node.js (88%), TypeScript (88%), MySQL (90%), and more web technologies.",
      keywords: "Kenenis skills, React developer skills, Node.js expertise, TypeScript developer, Full stack skills"
    },
    contact: {
      title: "Contact Kenenisa Kero (Kenenis) - Hire Full Stack Developer | Freelance Services",
      description: "Get in touch with Kenenisa Kero (Kenenis) for full stack development projects. Available for freelance work worldwide. React, Node.js, TypeScript expertise.",
      keywords: "Contact Kenenis, Hire Kenenisa Kero, Freelance Full Stack Developer, React developer for hire"
    },
    blog: {
      title: "Blog - Kenenisa Kero (Kenenis) | Web Development Insights & Tutorials",
      description: "Read web development articles, tutorials, and insights by Kenenisa Kero (Kenenis). Learn about React, Node.js, TypeScript, and modern web development practices.",
      keywords: "Kenenis blog, Web development blog, React tutorials, Node.js articles, TypeScript guides"
    }
  }
};

// Generate meta keywords string
export const getKeywordsString = (additionalKeywords: string[] = []): string => {
  return [...seoConfig.keywords, ...additionalKeywords].join(", ");
};

// Generate page-specific SEO data
export const getPageSEO = (page: keyof typeof seoConfig.pages, additionalData?: Partial<typeof seoConfig.pages.home>) => {
  const pageData = seoConfig.pages[page];
  return {
    ...pageData,
    ...additionalData,
    url: `${seoConfig.url}${page === 'home' ? '' : `/#${page}`}`,
    image: `${seoConfig.url}${seoConfig.images.ogImage}`
  };
};
