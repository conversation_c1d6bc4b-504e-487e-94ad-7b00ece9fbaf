
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Kenenis Dev - Personal Portfolio</title>
    <meta name="description" content="Kenenis Dev - Personal Portfolio Website - Showcase skills, projects, and experience" />
    <meta name="author" content="Kenenis Dev" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon-32x32.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon-16x16.svg" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Theme colors -->
    <meta name="theme-color" content="#ff014f" />
    <meta name="msapplication-TileColor" content="#1e293b" />

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Kenenis Dev" />
    <meta name="application-name" content="Kenenis Dev Portfolio" />
    <meta name="msapplication-starturl" content="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, viewport-fit=cover" />

    <meta property="og:title" content="Kenenis Dev - Personal Portfolio" />
    <meta property="og:description" content="Kenenis Dev - Personal Portfolio Website - Showcase skills, projects, and experience" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/apple-touch-icon.svg" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Kenenis Dev - Personal Portfolio" />
    <meta name="twitter:description" content="Kenenis Dev - Personal Portfolio Website" />
    <meta name="twitter:image" content="/apple-touch-icon.svg" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
