<!-- Meta tags for PNG version of OG image -->
<!-- Replace the current SVG meta tags with these once you have the PNG file -->

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website" />
<meta property="og:url" content="https://kenenis.com/" />
<meta property="og:title" content="Kenenisa Kero (Kenenis) - Full Stack Developer & UI/UX Designer | Bursa, Turkey" />
<meta property="og:description" content="Expert Full Stack Developer based in Bursa, Turkey, specializing in React, Node.js, TypeScript & UI/UX Design. 2+ years experience, 20+ projects completed. Available for remote work worldwide." />
<meta property="og:image" content="https://kenenis.com/og-image.png" />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta property="og:image:type" content="image/png" />
<meta property="og:site_name" content="Kenenisa Kero Portfolio" />

<!-- Twitter -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:url" content="https://kenenis.com/" />
<meta name="twitter:title" content="Kenenisa Kero (Kenenis) - Full Stack Developer & UI/UX Designer | Bursa, Turkey" />
<meta name="twitter:description" content="Expert Full Stack Developer based in Bursa, Turkey, specializing in React, Node.js, TypeScript & UI/UX Design. 2+ years experience, 20+ projects completed." />
<meta name="twitter:image" content="https://kenenis.com/og-image.png" />
<meta name="twitter:image:alt" content="Kenenisa Kero (Kenenis) Portfolio Logo" />
<meta name="twitter:creator" content="@kenenis" />
<meta name="twitter:site" content="@kenenis" />

<!-- Additional meta tags for better social sharing -->
<meta property="og:locale" content="en_US" />
<meta property="og:image:alt" content="Kenenisa Kero (Kenenis) Portfolio Logo" />
<meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />

<!-- Instructions for updating -->
<!-- 
1. Generate the PNG image using the og-image-generator.html file
2. Save it as "og-image.png" in your public folder
3. Replace the current meta tags in index.html with these
4. Test the image using Facebook Debugger and Twitter Card Validator
-->
